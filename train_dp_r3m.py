#!/usr/bin/env python3
"""
DP R3M 微调训练脚本

Usage:
    python train_dp_r3m.py
    
或者使用自定义参数:
    python train_dp_r3m.py --config-name=dp_r3m_finetune.yaml \
        task=gr1_dex-r3m \
        hydra.run.dir=data/outputs/gr1_dex-r3m-finetune_seed0 \
        training.debug=False \
        training.seed=0 \
        training.device="cuda:0" \
        exp_name=gr1_dex-r3m-finetune \
        logging.mode=offline \
        checkpoint.save_ckpt=True
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """主函数：执行DP R3M微调训练"""
    
    # 设置环境变量
    os.environ['WANDB_SILENT'] = "True"
    
    # 构建训练命令
    cmd = [
        "conda", "run", "-n", "idp3",  # 使用idp3环境
        "python", "Improved-3D-Diffusion-Policy/train.py",
        "--config-name=dp_r3m_finetune.yaml",
        "task=gr1_dex-r3m",
        "hydra.run.dir=data/outputs/gr1_dex-r3m-finetune_seed0",
        "training.debug=False",
        "training.seed=0",
        "training.device=cuda:0",
        "exp_name=gr1_dex-r3m-finetune",
        "logging.mode=offline",
        "checkpoint.save_ckpt=True",
        "task.dataset.zarr_path=/home/<USER>/code/opensource/Improved-3D-Diffusion-Policy/raw_pour_converted",
        "task.dataset.max_train_episodes=8",
        "task.dataset.val_ratio=0.11",
        "training.checkpoint_every=50"
    ]
    
    print("开始DP R3M微调训练...")
    print("训练命令:")
    print(" ".join(cmd))
    print("-" * 80)
    
    try:
        # 执行训练命令
        result = subprocess.run(cmd, check=True, capture_output=False)
        print("训练完成!")
        return result.returncode
        
    except subprocess.CalledProcessError as e:
        print(f"训练失败: {e}")
        return e.returncode
    except KeyboardInterrupt:
        print("训练被用户中断")
        return 1
    except Exception as e:
        print(f"发生错误: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
